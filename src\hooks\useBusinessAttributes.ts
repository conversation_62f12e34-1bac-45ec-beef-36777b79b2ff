import { useQuery } from "@tanstack/react-query";
import { businessAttributesApi } from "@/lib/api/businessAttributes";
import { queryKeys, mediumLivedQueryOptions } from "@/lib/query";
import type { BusinessAttributesFilters } from "@/types/businessAttributes";
import { useOrganizationContext } from "@/features/organizations/context";

export const useBusinessAttributes = (
	filters: BusinessAttributesFilters = {},
	options?: {
		enabled?: boolean;
	}
) => {
	const { organizationId } = useOrganizationContext();

	return useQuery({
		queryKey: queryKeys.businessAttributes.list(filters),
		queryFn: () =>
			businessAttributesApi.getBusinessAttributes(
				filters,
				organizationId || undefined
			),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};

export const useBusinessAttributesForForm = (options?: {
	enabled?: boolean;
}) => {
	const filters: BusinessAttributesFilters = {
		page: 1,
		per_page: 100,
		show_in_list: true,
	};

	return useBusinessAttributes(filters, options);
};

export const useConditionalAttributes = (
	organizationId: number,
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery({
		queryKey: ["conditionalAttributes", organizationId],
		queryFn: () =>
			businessAttributesApi.getConditionalAttributes(organizationId),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};

export const useAttributeTypeConfigs = (
	organizationId: number,
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery({
		queryKey: ["attributeTypeConfigs", organizationId],
		queryFn: () =>
			businessAttributesApi.getAttributeTypeConfigs(organizationId),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};

export const useClientAttributeSettings = (
	organizationId: number,
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery({
		queryKey: ["clientAttributeSettings", organizationId],
		queryFn: () =>
			businessAttributesApi.getClientAttributeSettings(organizationId),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};
