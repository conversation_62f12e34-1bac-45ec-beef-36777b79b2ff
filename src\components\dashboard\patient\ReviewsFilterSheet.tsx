import { useState } from "react";
import { X } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import MultiAsyncSelect from "@/components/common/MultiAsyncSelect";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import type { DateRange } from "@/components/common/Datepicker/DatePicker";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useAllStations } from "@/features/locations/hooks/useStations";
import { useServices } from "@/features/locations/hooks/useServices";
import { useCategories } from "@/hooks/useCategories";

export interface ReviewsFilterData {
	locations: string[];
	providers: string[];
	services: string[];
	categories: string[];
	dateRange: DateRange | undefined;
}

interface ReviewsFilterSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onApplyFilters: (filters: ReviewsFilterData) => void;
	onResetFilters: () => void;
}

export function ReviewsFilterSheet({
	open,
	onOpenChange,
	onApplyFilters,
	onResetFilters,
}: ReviewsFilterSheetProps) {
	const { organizationId } = useOrganizationContext();

	const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
	const [selectedProviders, setSelectedProviders] = useState<string[]>([]);
	const [selectedServices, setSelectedServices] = useState<string[]>([]);
	const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
	const [dateRange, setDateRange] = useState<DateRange | undefined>(
		undefined
	);

	// Fetch real data from APIs
	const { data: locationsData, isLoading: isLoadingLocations } = useLocations(
		{},
		organizationId || undefined
	);

	const { data: stationsData, isLoading: isLoadingStations } = useAllStations(
		{
			organizationId: organizationId || undefined,
			enabled: !!organizationId,
		}
	);

	const { data: servicesData, isLoading: isLoadingServices } = useServices({
		organizationId: organizationId || undefined,
		enabled: !!organizationId,
	});

	const { data: categoriesData, isLoading: isLoadingCategories } =
		useCategories(
			{},
			{
				enabled: !!organizationId,
			}
		);

	// Transform API data to options format
	const locationOptions = [
		{ value: "all", label: "All" },
		...(locationsData?.map((location) => ({
			value: location.id.toString(),
			label: location.name,
		})) || []),
	];

	const providerOptions = [
		{ value: "all", label: "All" },
		...(stationsData?.data?.map((station) => ({
			value: station.id.toString(),
			label: station.name,
		})) || []),
	];

	const serviceOptions = [
		{ value: "all", label: "All" },
		...(servicesData?.data?.map((service) => ({
			value: service.id.toString(),
			label: service.name,
		})) || []),
	];

	const categoryOptions = [
		{ value: "all", label: "All" },
		...(categoriesData?.data?.map((category) => ({
			value: category.id.toString(),
			label: category.name,
		})) || []),
	];

	const handleApply = () => {
		// Transform selected values to actual data for filtering
		const filters: ReviewsFilterData = {
			locations: selectedLocations.includes("all")
				? []
				: selectedLocations.filter((id) => id !== "all"),
			providers: selectedProviders.includes("all")
				? []
				: selectedProviders.filter((id) => id !== "all"),
			services: selectedServices.includes("all")
				? []
				: selectedServices.filter((id) => id !== "all"),
			categories: selectedCategories.includes("all")
				? []
				: selectedCategories.filter((id) => id !== "all"),
			dateRange,
		};
		onApplyFilters(filters);
		onOpenChange(false);
	};

	const handleReset = () => {
		setSelectedLocations([]);
		setSelectedProviders([]);
		setSelectedServices([]);
		setSelectedCategories([]);
		setDateRange(undefined);
		onResetFilters();
	};

	const handleCancel = () => {
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-9 py-9 sm:w-[540px] sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<SheetTitle className="flex items-center justify-between">
						<span>Filter Reviews</span>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleCancel}
							className="h-6 w-6"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetTitle>
					<p className="text-muted-foreground text-sm">
						Select options below to help filter your search
					</p>
				</SheetHeader>

				<div className="space-y-4 py-6">
					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Locations
						</Label>
						<MultiAsyncSelect
							options={locationOptions}
							onValueChange={setSelectedLocations}
							defaultValue={selectedLocations}
							placeholder={
								isLoadingLocations
									? "Loading locations..."
									: "Select locations"
							}
							className="w-full"
							disabled={isLoadingLocations}
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Providers
						</Label>
						<MultiAsyncSelect
							options={providerOptions}
							onValueChange={setSelectedProviders}
							defaultValue={selectedProviders}
							placeholder={
								isLoadingStations
									? "Loading providers..."
									: "Select providers"
							}
							className="w-full"
							disabled={isLoadingStations}
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Services
						</Label>
						<MultiAsyncSelect
							options={serviceOptions}
							onValueChange={setSelectedServices}
							defaultValue={selectedServices}
							placeholder={
								isLoadingServices
									? "Loading services..."
									: "Select services"
							}
							className="w-full"
							disabled={isLoadingServices}
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Categories
						</Label>
						<MultiAsyncSelect
							options={categoryOptions}
							onValueChange={setSelectedCategories}
							defaultValue={selectedCategories}
							placeholder={
								isLoadingCategories
									? "Loading categories..."
									: "Select categories"
							}
							className="w-full"
							disabled={isLoadingCategories}
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Select a Date or Range
						</Label>
						<DatePicker
							variant="range"
							value={dateRange}
							onChange={(range) =>
								setDateRange(range as DateRange)
							}
							placeholder="Pick a date"
							className="w-full"
						/>
					</div>
				</div>

				<SheetFooter className="flex-row justify-between">
					<Button
						variant="ghost"
						onClick={handleReset}
						className="text-muted-foreground hover:text-foreground"
					>
						Reset
					</Button>
					<div className="flex gap-3">
						<Button variant="outline" onClick={handleCancel}>
							Cancel
						</Button>
						<Button onClick={handleApply}>Apply</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
